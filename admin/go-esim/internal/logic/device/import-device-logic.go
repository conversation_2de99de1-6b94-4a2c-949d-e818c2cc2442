package device

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/operation"
	"github.com/jackc/pgx/v5"
	"github.com/rabbitmq/amqp091-go"
	"github.com/zeromicro/go-zero/core/logx"
)

type ImportDeviceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewImportDeviceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ImportDeviceLogic {
	return &ImportDeviceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ImportDeviceLogic) ImportDevice(req *types.ImportDeviceReq) (resp *types.ImportDeviceResp, err error) {
	// 1. 创建任务记录
	taskID, err := l.createTask(req)
	if err != nil {
		return nil, err
	}
	var targetId int32
	if taskID != 0 {
		targetId = int32(taskID)
	}
	err = operation.DBLog(l.ctx, l.svcCtx.DB, "导入设备数据", &targetId, "iot.task", fmt.Sprintf("导入文件: %s", req.File.Name), map[string]interface{}{
		"file_name":  req.File.Name,
		"file_url":   req.File.URL,
		"cost":       req.Cost,
		"channel_id": req.Channel,
	})
	if err != nil {
		logx.Errorf("导入设备数据失败: %v", err)
		err = nil
	}

	// 2. 发送消息到 RabbitMQ
	message := map[string]interface{}{
		"task_id":       taskID,
		"file_name":     req.File.Name,
		"file_url":      req.File.URL,
		"cost":          req.Cost,
		"channel_id":    req.Channel,
		"tags":          req.Tags,
		"cover":         req.Cover,
		"device_type":   req.DeviceType,
		"model":         req.Model,
		"serial_prefix": req.SerialPrefix,
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		return nil, err
	}

	err = l.svcCtx.RabbitMQ.SendMessageClose(l.ctx, "device_import", "device_import", true, amqp091.Publishing{
		Body: messageBytes,
	})
	if err != nil {
		return nil, err
	}

	return &types.ImportDeviceResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "任务已创建，正在处理中",
		},
	}, nil
}

// createTask 创建任务记录
func (l *ImportDeviceLogic) createTask(req *types.ImportDeviceReq) (int, error) {
	var taskID int
	args := make(pgx.NamedArgs)
	args["type"] = 2 // 2: 设备导入
	args["operator_id"] = l.ctx.Value("userId").(string)
	args["status"] = 1 // 1: 待处理
	args["ext_data"] = map[string]interface{}{
		"file":          req.File,
		"cost":          req.Cost,
		"channel":       req.Channel,
		"tags":          req.Tags,
		"cover":         req.Cover,
		"device_type":   req.DeviceType,
		"model":         req.Model,
		"serial_prefix": req.SerialPrefix,
	}

	query := `
		INSERT INTO iot.task (
			type, operator_id, status, ext_data
		) VALUES (
			@type, @operator_id, @status, @ext_data
		) RETURNING id
	`

	err := l.svcCtx.DB.QueryRow(l.ctx, query, args).Scan(&taskID)
	if err != nil {
		return 0, err
	}

	return taskID, nil
}

// updateTaskStatus 更新任务状态
func (l *ImportDeviceLogic) updateTaskStatus(taskID int, status int, successRecords, totalRecords, failedRecords *int, errorMessage *string) error {
	args := make(pgx.NamedArgs)
	args["id"] = taskID
	args["status"] = status
	args["updated_at"] = time.Now()

	query := `
		UPDATE iot.task SET
			status = @status,
			updated_at = @updated_at
	`

	if successRecords != nil {
		args["success_records"] = *successRecords
		query += ", success_records = @success_records"
	}
	if totalRecords != nil {
		args["total_records"] = *totalRecords
		query += ", total_records = @total_records"
	}
	if failedRecords != nil {
		args["failed_records"] = *failedRecords
		query += ", failed_records = @failed_records"
	}
	if errorMessage != nil {
		args["error_message"] = *errorMessage
		query += ", error_message = @error_message"
	}

	query += " WHERE id = @id"

	_, err := l.svcCtx.DB.Exec(l.ctx, query, args)
	return err
}
