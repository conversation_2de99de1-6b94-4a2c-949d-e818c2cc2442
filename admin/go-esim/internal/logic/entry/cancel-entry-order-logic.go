package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelEntryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelEntryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelEntryOrderLogic {
	return &CancelEntryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CancelEntryOrderLogic) CancelEntryOrder(req *types.CancelEntryOrderReq) (resp *types.CancelEntryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
