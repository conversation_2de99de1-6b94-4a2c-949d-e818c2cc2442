package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetEntryOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetEntryOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetEntryOrderListLogic {
	return &GetEntryOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetEntryOrderListLogic) GetEntryOrderList(req *types.GetEntryOrderListReq) (resp *types.GetEntryOrderListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
