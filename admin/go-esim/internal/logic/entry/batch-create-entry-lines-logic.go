package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchCreateEntryLinesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchCreateEntryLinesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchCreateEntryLinesLogic {
	return &BatchCreateEntryLinesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *BatchCreateEntryLinesLogic) BatchCreateEntryLines(req *types.BatchCreateEntryLinesReq) (resp *types.BatchCreateEntryLinesResp, err error) {
	// todo: add your logic here and delete this line

	return
}
