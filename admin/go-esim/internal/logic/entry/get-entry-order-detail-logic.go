package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetEntryOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetEntryOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetEntryOrderDetailLogic {
	return &GetEntryOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetEntryOrderDetailLogic) GetEntryOrderDetail(req *types.GetEntryOrderDetailReq) (resp *types.GetEntryOrderDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
