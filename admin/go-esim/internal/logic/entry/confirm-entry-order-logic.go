package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConfirmEntryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConfirmEntryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConfirmEntryOrderLogic {
	return &ConfirmEntryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ConfirmEntryOrderLogic) ConfirmEntryOrder(req *types.ConfirmEntryOrderReq) (resp *types.ConfirmEntryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
