package qimen

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncEntryOrderToQimenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncEntryOrderToQimenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncEntryOrderToQimenLogic {
	return &SyncEntryOrderToQimenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *SyncEntryOrderToQimenLogic) SyncEntryOrderToQimen(req *types.SyncEntryOrderToQimenReq) (resp *types.SyncEntryOrderToQimenResp, err error) {
	// todo: add your logic here and delete this line

	return
}
