package db

import (
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
)

// 封装一个插入数据的方法
func Insert(ctx context.Context, table string, params pgx.NamedArgs, f Exec) error {
	// 列
	var columns []string
	// 值
	var values []string
	for key := range params {
		columns = append(columns, key)
		values = append(values, fmt.Sprintf("@%s", key))
	}

	sql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)", table, strings.Join(columns, ","), strings.Join(values, ","))
	_, err := f(ctx, sql, params)
	return err
}
