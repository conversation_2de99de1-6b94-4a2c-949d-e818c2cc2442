create table iot.warehouses
(
    id                serial
        primary key,
    warehouse_code    varchar(50)  not null
        unique,
    warehouse_name    varchar(100) not null,
    company_name      varchar(200),
    contact_person    varchar(50),
    contact_phone     varchar(20),
    contact_email     varchar(100),
    address           text,
    province          varchar(50),
    city              varchar(50),
    district          varchar(50),
    api_endpoint      varchar(200),
    app_key           varchar(100),
    app_secret        varchar(100),
    customer_id       varchar(50),
    bank_name         varchar(100),
    bank_account      varchar(50),
    bank_account_name varchar(100),
    status            integer   default 1,
    created_at        timestamp default CURRENT_TIMESTAMP,
    updated_at        timestamp default CURRENT_TIMESTAMP
);

comment on table iot.warehouses is '云仓基础信息';

comment on column iot.warehouses.warehouse_code is '云仓编码';

comment on column iot.warehouses.warehouse_name is '云仓名称';

comment on column iot.warehouses.company_name is '云仓公司名称';

comment on column iot.warehouses.contact_person is '联系人姓名';

comment on column iot.warehouses.contact_phone is '联系电话';

comment on column iot.warehouses.contact_email is '联系邮箱';

comment on column iot.warehouses.address is '仓库地址';

comment on column iot.warehouses.province is '省份';

comment on column iot.warehouses.city is '城市';

comment on column iot.warehouses.district is '区县';

comment on column iot.warehouses.api_endpoint is 'API接口地址';

comment on column iot.warehouses.app_key is '奇门API的appkey';

comment on column iot.warehouses.app_secret is '奇门API的secret';

comment on column iot.warehouses.customer_id is '奇门API的客户ID';

comment on column iot.warehouses.bank_name is '银行名称';

comment on column iot.warehouses.bank_account is '银行账号';

comment on column iot.warehouses.bank_account_name is '开户名';

comment on column iot.warehouses.status is '状态：1-正常，2-停用';

comment on column iot.warehouses.created_at is '创建时间';

comment on column iot.warehouses.updated_at is '更新时间';

alter table iot.warehouses
    owner to postgres;

create index idx_warehouses_code
    on iot.warehouses (warehouse_code);

create index idx_warehouses_status
    on iot.warehouses (status);

create table iot.warehouse_entry_orders
(
    id                   serial
        primary key,
    entry_order_code     varchar(50)                                    not null
        unique,
    warehouse_code       varchar(50)                                    not null,
    warehouse_name       varchar(100),
    owner_code           varchar(50) default 'NIUYI'::character varying not null,
    order_type           varchar(20) default 'CGRK'::character varying,
    order_create_time    timestamp,
    purchase_order_code  varchar(50),
    expect_start_time    timestamp,
    expect_end_time      timestamp,
    actual_arrival_time  timestamp,
    logistics_code       varchar(20),
    logistics_name       varchar(100),
    express_code         varchar(50),
    supplier_code        varchar(50),
    supplier_name        varchar(100),
    operator_code        varchar(50),
    operator_name        varchar(50),
    operate_time         timestamp,
    total_order_lines    integer     default 0,
    expected_quantity    integer     default 0,
    actual_quantity      integer     default 0,
    order_status         integer     default 1,
    remark               text,
    extend_props         jsonb,
    qimen_entry_order_id varchar(50),
    api_sync_status      integer     default 0,
    api_sync_time        timestamp,
    api_error_message    text,
    created_at           timestamp   default CURRENT_TIMESTAMP,
    updated_at           timestamp   default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_entry_orders is '云仓入库单表 ';

comment on column iot.warehouse_entry_orders.entry_order_code is '入库单号';

comment on column iot.warehouse_entry_orders.warehouse_code is '仓库编码';

comment on column iot.warehouse_entry_orders.warehouse_name is '仓库名称';

comment on column iot.warehouse_entry_orders.owner_code is '货主编码(默认纽翼)';

comment on column iot.warehouse_entry_orders.order_type is '业务类型(CGRK=采购入库;DBRK=调拨入库等)';

comment on column iot.warehouse_entry_orders.order_create_time is '订单创建时间';

comment on column iot.warehouse_entry_orders.purchase_order_code is '采购单号';

comment on column iot.warehouse_entry_orders.expect_start_time is '预期到货时间';

comment on column iot.warehouse_entry_orders.expect_end_time is '最迟预期到货时间';

comment on column iot.warehouse_entry_orders.actual_arrival_time is '实际到货时间';

comment on column iot.warehouse_entry_orders.logistics_code is '物流公司编码(SF=顺丰等)';

comment on column iot.warehouse_entry_orders.logistics_name is '物流公司名称';

comment on column iot.warehouse_entry_orders.express_code is '运单号';

comment on column iot.warehouse_entry_orders.supplier_code is '供应商编码';

comment on column iot.warehouse_entry_orders.supplier_name is '供应商名称';

comment on column iot.warehouse_entry_orders.operator_code is '操作员编码';

comment on column iot.warehouse_entry_orders.operator_name is '操作员姓名';

comment on column iot.warehouse_entry_orders.operate_time is '操作时间';

comment on column iot.warehouse_entry_orders.total_order_lines is '单据行数';

comment on column iot.warehouse_entry_orders.expected_quantity is '预期数量';

comment on column iot.warehouse_entry_orders.actual_quantity is '实际数量';

comment on column iot.warehouse_entry_orders.order_status is '订单状态：1-待入库 2-部分入库 3-全部入库 4-异常 5-取消';

comment on column iot.warehouse_entry_orders.remark is '备注';

comment on column iot.warehouse_entry_orders.extend_props is '扩展属性';

comment on column iot.warehouse_entry_orders.qimen_entry_order_id is '奇门系统返回的入库单ID';

comment on column iot.warehouse_entry_orders.api_sync_status is 'API同步状态：0-未同步 1-已同步 2-同步失败';

comment on column iot.warehouse_entry_orders.api_sync_time is 'API同步时间';

comment on column iot.warehouse_entry_orders.api_error_message is 'API错误信息';

comment on column iot.warehouse_entry_orders.created_at is '创建时间';

comment on column iot.warehouse_entry_orders.updated_at is '更新时间';

alter table iot.warehouse_entry_orders
    owner to postgres;

create index idx_entry_orders_code
    on iot.warehouse_entry_orders (entry_order_code);

create index idx_entry_orders_warehouse
    on iot.warehouse_entry_orders (warehouse_code);

create index idx_entry_orders_status
    on iot.warehouse_entry_orders (order_status);

create index idx_entry_orders_supplier
    on iot.warehouse_entry_orders (supplier_code);

create table iot.warehouse_entry_order_lines
(
    id               serial
        primary key,
    entry_order_id   integer                                        not null,
    entry_order_code varchar(50)                                    not null,
    order_line_no    varchar(50),
    out_biz_code     varchar(50),
    owner_code       varchar(50) default 'NIUYI'::character varying not null,
    item_code        varchar(100)                                   not null,
    item_id          varchar(50),
    item_name        varchar(200),
    sku_property     varchar(500),
    plan_qty         integer     default 1                          not null,
    actual_qty       integer     default 0,
    purchase_price   numeric(10, 2),
    retail_price     numeric(10, 2),
    inventory_type   varchar(10) default 'ZP'::character varying,
    batch_code       varchar(50),
    produce_code     varchar(50),
    product_date     date,
    expire_date      date,
    box_number       varchar(50),
    pallet_number    varchar(50),
    unit             varchar(20) default '台'::character varying,
    sn_codes         jsonb,
    shelf_location   varchar(50),
    inbound_time     timestamp,
    device_status    integer     default 1,
    remark           text,
    extend_props     jsonb,
    created_at       timestamp   default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_entry_order_lines is '云仓入库单明细表';

comment on column iot.warehouse_entry_order_lines.entry_order_id is '入库单ID，引用自入库单表';

comment on column iot.warehouse_entry_order_lines.entry_order_code is '入库单号';

comment on column iot.warehouse_entry_order_lines.order_line_no is '入库单的行号';

comment on column iot.warehouse_entry_order_lines.out_biz_code is '外部业务编码';

comment on column iot.warehouse_entry_order_lines.owner_code is '货主编码';

comment on column iot.warehouse_entry_order_lines.item_code is '商品编码(设备序列号)';

comment on column iot.warehouse_entry_order_lines.item_id is '仓储系统商品ID';

comment on column iot.warehouse_entry_order_lines.item_name is '商品名称';

comment on column iot.warehouse_entry_order_lines.sku_property is '商品属性';

comment on column iot.warehouse_entry_order_lines.plan_qty is '应收商品数量(设备通常为1)';

comment on column iot.warehouse_entry_order_lines.actual_qty is '实际入库数量';

comment on column iot.warehouse_entry_order_lines.purchase_price is '采购价';

comment on column iot.warehouse_entry_order_lines.retail_price is '零售价';

comment on column iot.warehouse_entry_order_lines.inventory_type is '库存类型(ZP=正品;CC=残次等)';

comment on column iot.warehouse_entry_order_lines.batch_code is '批次编码';

comment on column iot.warehouse_entry_order_lines.produce_code is '生产批号';

comment on column iot.warehouse_entry_order_lines.product_date is '商品生产日期';

comment on column iot.warehouse_entry_order_lines.expire_date is '商品过期日期';

comment on column iot.warehouse_entry_order_lines.box_number is '箱号';

comment on column iot.warehouse_entry_order_lines.pallet_number is '卡板号';

comment on column iot.warehouse_entry_order_lines.unit is '单位';

comment on column iot.warehouse_entry_order_lines.sn_codes is 'SN编码列表';

comment on column iot.warehouse_entry_order_lines.shelf_location is '上架位置';

comment on column iot.warehouse_entry_order_lines.inbound_time is '实际入库时间';

comment on column iot.warehouse_entry_order_lines.device_status is '设备状态：1-正常 2-损坏 3-缺失配件';

comment on column iot.warehouse_entry_order_lines.remark is '备注';

comment on column iot.warehouse_entry_order_lines.extend_props is '扩展属性';

comment on column iot.warehouse_entry_order_lines.created_at is '创建时间';

alter table iot.warehouse_entry_order_lines
    owner to postgres;

create index idx_entry_lines_order_id
    on iot.warehouse_entry_order_lines (entry_order_id);

create index idx_entry_lines_item_code
    on iot.warehouse_entry_order_lines (item_code);

create index idx_entry_lines_batch
    on iot.warehouse_entry_order_lines (batch_code);

create table iot.warehouse_delivery_orders
(
    id                      serial
        primary key,
    delivery_order_code     varchar(50)                                       not null
        unique,
    warehouse_code          varchar(50)                                       not null,
    warehouse_name          varchar(100),
    owner_code              varchar(50)    default 'NIUYI'::character varying not null,
    order_type              varchar(20)    default 'JYCK'::character varying,
    order_create_time       timestamp,
    priority                integer        default 1,
    receiver_name           varchar(100)                                      not null,
    receiver_phone          varchar(20)                                       not null,
    receiver_mobile         varchar(20),
    receiver_email          varchar(100),
    receiver_id_type        varchar(10),
    receiver_id_number      varchar(50),
    receiver_province       varchar(50)                                       not null,
    receiver_city           varchar(50)                                       not null,
    receiver_area           varchar(50),
    receiver_town           varchar(50),
    receiver_address        text                                              not null,
    receiver_zip_code       varchar(20),
    recipient_type          integer                                           not null,
    recipient_id            integer,
    trade_order_code        varchar(50),
    expect_start_time       timestamp,
    expect_end_time         timestamp,
    logistics_code          varchar(20),
    logistics_name          varchar(100),
    express_code            varchar(50),
    express_fee             numeric(10, 2) default 0,
    total_order_lines       integer        default 0,
    requested_quantity      integer        default 0,
    actual_quantity         integer        default 0,
    order_status            integer        default 1,
    ship_time               timestamp,
    receive_time            timestamp,
    operator_code           varchar(50),
    operator_name           varchar(50),
    operate_time            timestamp,
    remark                  text,
    extend_props            jsonb,
    qimen_delivery_order_id varchar(50),
    api_sync_status         integer        default 0,
    api_sync_time           timestamp,
    api_error_message       text,
    created_at              timestamp      default CURRENT_TIMESTAMP,
    updated_at              timestamp      default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_delivery_orders is '云仓出库单表';

comment on column iot.warehouse_delivery_orders.delivery_order_code is '出库单号';

comment on column iot.warehouse_delivery_orders.warehouse_code is '仓库编码';

comment on column iot.warehouse_delivery_orders.warehouse_name is '仓库名称';

comment on column iot.warehouse_delivery_orders.owner_code is '货主编码';

comment on column iot.warehouse_delivery_orders.order_type is '业务类型(JYCK=交易出库;DBCK=调拨出库等)';

comment on column iot.warehouse_delivery_orders.order_create_time is '订单创建时间';

comment on column iot.warehouse_delivery_orders.priority is '优先级';

comment on column iot.warehouse_delivery_orders.receiver_name is '收货人姓名';

comment on column iot.warehouse_delivery_orders.receiver_phone is '收货人电话';

comment on column iot.warehouse_delivery_orders.receiver_mobile is '收货人手机';

comment on column iot.warehouse_delivery_orders.receiver_email is '收货人邮箱';

comment on column iot.warehouse_delivery_orders.receiver_id_type is '收件人证件类型';

comment on column iot.warehouse_delivery_orders.receiver_id_number is '收件人证件号码';

comment on column iot.warehouse_delivery_orders.receiver_province is '省份';

comment on column iot.warehouse_delivery_orders.receiver_city is '城市';

comment on column iot.warehouse_delivery_orders.receiver_area is '区域';

comment on column iot.warehouse_delivery_orders.receiver_town is '村镇';

comment on column iot.warehouse_delivery_orders.receiver_address is '详细地址';

comment on column iot.warehouse_delivery_orders.receiver_zip_code is '邮编';

comment on column iot.warehouse_delivery_orders.recipient_type is '收货人类型：1-合伙人 2-代理商 3-用户';

comment on column iot.warehouse_delivery_orders.recipient_id is '收货人ID';

comment on column iot.warehouse_delivery_orders.trade_order_code is '交易订单号';

comment on column iot.warehouse_delivery_orders.expect_start_time is '预期发货开始时间';

comment on column iot.warehouse_delivery_orders.expect_end_time is '预期发货结束时间';

comment on column iot.warehouse_delivery_orders.logistics_code is '物流公司编码';

comment on column iot.warehouse_delivery_orders.logistics_name is '物流公司名称';

comment on column iot.warehouse_delivery_orders.express_code is '运单号';

comment on column iot.warehouse_delivery_orders.express_fee is '快递费用';

comment on column iot.warehouse_delivery_orders.total_order_lines is '单据行数';

comment on column iot.warehouse_delivery_orders.requested_quantity is '申请数量';

comment on column iot.warehouse_delivery_orders.actual_quantity is '实际发货数量';

comment on column iot.warehouse_delivery_orders.order_status is '订单状态：1-待出库 2-已出库 3-已发货 4-已收货 5-异常 6-取消';

comment on column iot.warehouse_delivery_orders.ship_time is '发货时间';

comment on column iot.warehouse_delivery_orders.receive_time is '收货时间';

comment on column iot.warehouse_delivery_orders.operator_code is '操作员编码';

comment on column iot.warehouse_delivery_orders.operator_name is '操作员姓名';

comment on column iot.warehouse_delivery_orders.operate_time is '操作时间';

comment on column iot.warehouse_delivery_orders.remark is '备注';

comment on column iot.warehouse_delivery_orders.extend_props is '扩展属性';

comment on column iot.warehouse_delivery_orders.qimen_delivery_order_id is '奇门系统返回的出库单ID';

comment on column iot.warehouse_delivery_orders.api_sync_status is 'API同步状态';

comment on column iot.warehouse_delivery_orders.api_sync_time is 'API同步时间';

comment on column iot.warehouse_delivery_orders.api_error_message is 'API错误信息';

comment on column iot.warehouse_delivery_orders.created_at is '创建时间';

comment on column iot.warehouse_delivery_orders.updated_at is '更新时间';

alter table iot.warehouse_delivery_orders
    owner to postgres;

create index idx_delivery_orders_code
    on iot.warehouse_delivery_orders (delivery_order_code);

create index idx_delivery_orders_warehouse
    on iot.warehouse_delivery_orders (warehouse_code);

create index idx_delivery_orders_status
    on iot.warehouse_delivery_orders (order_status);

create index idx_delivery_orders_recipient
    on iot.warehouse_delivery_orders (recipient_type, recipient_id);

create index idx_delivery_orders_trade
    on iot.warehouse_delivery_orders (trade_order_code);

create table iot.warehouse_delivery_order_lines
(
    id                      serial
        primary key,
    delivery_order_id       integer                                        not null,
    delivery_order_code     varchar(50)                                    not null,
    order_line_no           varchar(50),
    out_biz_code            varchar(50),
    owner_code              varchar(50) default 'NIUYI'::character varying not null,
    item_code               varchar(100)                                   not null,
    item_id                 varchar(50),
    item_name               varchar(200),
    sku_property            varchar(500),
    plan_qty                integer     default 1                          not null,
    actual_qty              integer     default 0,
    retail_price            numeric(10, 2),
    inventory_type          varchar(10) default 'ZP'::character varying,
    batch_code              varchar(50),
    produce_code            varchar(50),
    original_shelf_location varchar(50),
    original_box_number     varchar(50),
    original_pallet_number  varchar(50),
    unit                    varchar(20) default '台'::character varying,
    sn_codes                jsonb,
    outbound_time           timestamp,
    remark                  text,
    extend_props            jsonb,
    created_at              timestamp   default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_delivery_order_lines is '云仓出库单明细表';

comment on column iot.warehouse_delivery_order_lines.delivery_order_id is '出库单ID，引用自出库单表';

comment on column iot.warehouse_delivery_order_lines.delivery_order_code is '出库单号';

comment on column iot.warehouse_delivery_order_lines.order_line_no is '出库单的行号';

comment on column iot.warehouse_delivery_order_lines.out_biz_code is '外部业务编码';

comment on column iot.warehouse_delivery_order_lines.owner_code is '货主编码';

comment on column iot.warehouse_delivery_order_lines.item_code is '商品编码(设备序列号)';

comment on column iot.warehouse_delivery_order_lines.item_id is '仓储系统商品ID';

comment on column iot.warehouse_delivery_order_lines.item_name is '商品名称';

comment on column iot.warehouse_delivery_order_lines.sku_property is '商品属性';

comment on column iot.warehouse_delivery_order_lines.plan_qty is '应发商品数量';

comment on column iot.warehouse_delivery_order_lines.actual_qty is '实际出库数量';

comment on column iot.warehouse_delivery_order_lines.retail_price is '零售价';

comment on column iot.warehouse_delivery_order_lines.inventory_type is '库存类型';

comment on column iot.warehouse_delivery_order_lines.batch_code is '批次编码';

comment on column iot.warehouse_delivery_order_lines.produce_code is '生产批号';

comment on column iot.warehouse_delivery_order_lines.original_shelf_location is '原货架位置';

comment on column iot.warehouse_delivery_order_lines.original_box_number is '原箱号';

comment on column iot.warehouse_delivery_order_lines.original_pallet_number is '原卡板号';

comment on column iot.warehouse_delivery_order_lines.unit is '单位';

comment on column iot.warehouse_delivery_order_lines.sn_codes is 'SN编码列表';

comment on column iot.warehouse_delivery_order_lines.outbound_time is '实际出库时间';

comment on column iot.warehouse_delivery_order_lines.remark is '备注';

comment on column iot.warehouse_delivery_order_lines.extend_props is '扩展属性';

comment on column iot.warehouse_delivery_order_lines.created_at is '创建时间';

alter table iot.warehouse_delivery_order_lines
    owner to postgres;

create index idx_delivery_lines_order_id
    on iot.warehouse_delivery_order_lines (delivery_order_id);

create index idx_delivery_lines_item_code
    on iot.warehouse_delivery_order_lines (item_code);

create index idx_delivery_lines_batch
    on iot.warehouse_delivery_order_lines (batch_code);

create table iot.warehouse_inventory
(
    id                  serial
        primary key,
    warehouse_code      varchar(50)                                    not null,
    owner_code          varchar(50) default 'NIUYI'::character varying not null,
    item_code           varchar(100)                                   not null,
    item_id             varchar(50),
    item_name           varchar(200),
    available_qty       integer     default 0,
    locked_qty          integer     default 0,
    picked_qty          integer     default 0,
    total_qty           integer     default 0,
    inventory_type      varchar(10) default 'ZP'::character varying,
    inventory_status    integer     default 1,
    batch_code          varchar(50),
    produce_code        varchar(50),
    product_date        date,
    expire_date         date,
    shelf_location      varchar(50),
    box_number          varchar(50),
    pallet_number       varchar(50),
    in_warehouse_time   timestamp,
    out_warehouse_time  timestamp,
    last_inventory_time timestamp,
    in_batch_no         varchar(50),
    out_batch_no        varchar(50),
    remark              text,
    extend_props        jsonb,
    created_at          timestamp   default CURRENT_TIMESTAMP,
    updated_at          timestamp   default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_inventory is '云仓库存表';

comment on column iot.warehouse_inventory.warehouse_code is '仓库编码';

comment on column iot.warehouse_inventory.owner_code is '货主编码';

comment on column iot.warehouse_inventory.item_code is '商品编码(设备序列号)';

comment on column iot.warehouse_inventory.item_id is '仓储系统商品ID';

comment on column iot.warehouse_inventory.item_name is '商品名称';

comment on column iot.warehouse_inventory.available_qty is '可用库存';

comment on column iot.warehouse_inventory.locked_qty is '锁定库存';

comment on column iot.warehouse_inventory.picked_qty is '拣货库存';

comment on column iot.warehouse_inventory.total_qty is '总库存';

comment on column iot.warehouse_inventory.inventory_type is '库存类型';

comment on column iot.warehouse_inventory.inventory_status is '库存状态：1-在库 2-预出库 3-已出库 4-损坏 5-丢失';

comment on column iot.warehouse_inventory.batch_code is '批次编码';

comment on column iot.warehouse_inventory.produce_code is '生产批号';

comment on column iot.warehouse_inventory.product_date is '生产日期';

comment on column iot.warehouse_inventory.expire_date is '过期日期';

comment on column iot.warehouse_inventory.shelf_location is '货架位置';

comment on column iot.warehouse_inventory.box_number is '箱号';

comment on column iot.warehouse_inventory.pallet_number is '卡板号';

comment on column iot.warehouse_inventory.in_warehouse_time is '入库时间';

comment on column iot.warehouse_inventory.out_warehouse_time is '出库时间';

comment on column iot.warehouse_inventory.last_inventory_time is '最后盘点时间';

comment on column iot.warehouse_inventory.in_batch_no is '入库批次号';

comment on column iot.warehouse_inventory.out_batch_no is '出库批次号';

comment on column iot.warehouse_inventory.remark is '备注';

comment on column iot.warehouse_inventory.extend_props is '扩展属性';

comment on column iot.warehouse_inventory.created_at is '创建时间';

comment on column iot.warehouse_inventory.updated_at is '更新时间';

alter table iot.warehouse_inventory
    owner to postgres;

create index idx_inventory_warehouse
    on iot.warehouse_inventory (warehouse_code);

create index idx_inventory_item
    on iot.warehouse_inventory (item_code);

create index idx_inventory_status
    on iot.warehouse_inventory (inventory_status);

create index idx_inventory_batch
    on iot.warehouse_inventory (batch_code);

create unique index idx_inventory_unique
    on iot.warehouse_inventory (warehouse_code, item_code, batch_code, inventory_type);

create table iot.warehouse_inventory_logs
(
    id                 serial
        primary key,
    warehouse_code     varchar(50)                                    not null,
    owner_code         varchar(50) default 'NIUYI'::character varying not null,
    item_code          varchar(100)                                   not null,
    item_name          varchar(200),
    operation_type     integer                                        not null,
    operation_desc     varchar(100),
    operation_quantity integer     default 0,
    before_qty         integer     default 0,
    after_qty          integer     default 0,
    related_order_id   integer,
    related_order_code varchar(50),
    related_order_type integer,
    batch_code         varchar(50),
    inventory_type     varchar(10) default 'ZP'::character varying,
    operator_id        integer,
    operator_name      varchar(50),
    operation_time     timestamp   default CURRENT_TIMESTAMP,
    remark             text,
    created_at         timestamp   default CURRENT_TIMESTAMP
);

comment on table iot.warehouse_inventory_logs is '云仓库存变动日志表';

comment on column iot.warehouse_inventory_logs.warehouse_code is '仓库编码';

comment on column iot.warehouse_inventory_logs.owner_code is '货主编码';

comment on column iot.warehouse_inventory_logs.item_code is '商品编码';

comment on column iot.warehouse_inventory_logs.item_name is '商品名称';

comment on column iot.warehouse_inventory_logs.operation_type is '操作类型：1-入库 2-出库 3-盘点 4-调拨 5-报损 6-报溢';

comment on column iot.warehouse_inventory_logs.operation_desc is '操作描述';

comment on column iot.warehouse_inventory_logs.operation_quantity is '操作数量（正数为增加，负数为减少）';

comment on column iot.warehouse_inventory_logs.before_qty is '操作前数量';

comment on column iot.warehouse_inventory_logs.after_qty is '操作后数量';

comment on column iot.warehouse_inventory_logs.related_order_id is '关联订单ID';

comment on column iot.warehouse_inventory_logs.related_order_code is '关联订单号';

comment on column iot.warehouse_inventory_logs.related_order_type is '关联订单类型：1-入库单 2-出库单 3-盘点单';

comment on column iot.warehouse_inventory_logs.batch_code is '批次编码';

comment on column iot.warehouse_inventory_logs.inventory_type is '库存类型';

comment on column iot.warehouse_inventory_logs.operator_id is '操作员ID';

comment on column iot.warehouse_inventory_logs.operator_name is '操作员姓名';

comment on column iot.warehouse_inventory_logs.operation_time is '操作时间';

comment on column iot.warehouse_inventory_logs.remark is '备注';

comment on column iot.warehouse_inventory_logs.created_at is '创建时间';

alter table iot.warehouse_inventory_logs
    owner to postgres;

create index idx_inventory_logs_warehouse
    on iot.warehouse_inventory_logs (warehouse_code);

create index idx_inventory_logs_item
    on iot.warehouse_inventory_logs (item_code);

create index idx_inventory_logs_operation_type
    on iot.warehouse_inventory_logs (operation_type);

create index idx_inventory_logs_operation_time
    on iot.warehouse_inventory_logs (operation_time);

create index idx_inventory_logs_related_order
    on iot.warehouse_inventory_logs (related_order_code);

create table iot.qimen_api_sync_logs
(
    id                 serial
        primary key,
    warehouse_code     varchar(50)  not null,
    api_method         varchar(100) not null,
    sync_type          integer      not null,
    sync_direction     integer      not null,
    related_order_id   integer,
    related_order_code varchar(50),
    request_data       jsonb,
    response_data      jsonb,
    sync_status        integer   default 1,
    error_code         varchar(50),
    error_message      text,
    retry_count        integer   default 0,
    max_retry_count    integer   default 3,
    next_retry_time    timestamp,
    sync_time          timestamp default CURRENT_TIMESTAMP,
    response_time      integer,
    created_at         timestamp default CURRENT_TIMESTAMP
);

comment on table iot.qimen_api_sync_logs is '奇门API同步日志表';

comment on column iot.qimen_api_sync_logs.warehouse_code is '仓库编码';

comment on column iot.qimen_api_sync_logs.api_method is 'API方法名(如taobao.qimen.entryorder.create)';

comment on column iot.qimen_api_sync_logs.sync_type is '同步类型：1-入库单 2-出库单 3-库存查询 4-库存同步';

comment on column iot.qimen_api_sync_logs.sync_direction is '同步方向：1-推送到云仓 2-从云仓拉取';

comment on column iot.qimen_api_sync_logs.related_order_id is '关联订单ID';

comment on column iot.qimen_api_sync_logs.related_order_code is '关联订单号';

comment on column iot.qimen_api_sync_logs.request_data is 'API请求数据';

comment on column iot.qimen_api_sync_logs.response_data is 'API响应数据';

comment on column iot.qimen_api_sync_logs.sync_status is '同步状态：1-成功 2-失败 3-部分成功';

comment on column iot.qimen_api_sync_logs.error_code is '错误码';

comment on column iot.qimen_api_sync_logs.error_message is '错误信息';

comment on column iot.qimen_api_sync_logs.retry_count is '重试次数';

comment on column iot.qimen_api_sync_logs.max_retry_count is '最大重试次数';

comment on column iot.qimen_api_sync_logs.next_retry_time is '下次重试时间';

comment on column iot.qimen_api_sync_logs.sync_time is '同步时间';

comment on column iot.qimen_api_sync_logs.response_time is '响应时间(毫秒)';

comment on column iot.qimen_api_sync_logs.created_at is '创建时间';

alter table iot.qimen_api_sync_logs
    owner to postgres;

create index idx_qimen_logs_warehouse
    on iot.qimen_api_sync_logs (warehouse_code);

create index idx_qimen_logs_method
    on iot.qimen_api_sync_logs (api_method);

create index idx_qimen_logs_sync_type
    on iot.qimen_api_sync_logs (sync_type);

create index idx_qimen_logs_status
    on iot.qimen_api_sync_logs (sync_status);

create index idx_qimen_logs_sync_time
    on iot.qimen_api_sync_logs (sync_time);

create index idx_qimen_logs_related_order
    on iot.qimen_api_sync_logs (related_order_code);

