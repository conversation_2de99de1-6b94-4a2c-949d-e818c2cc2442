syntax = "v1"
import "base.api"

type ImportDeviceReq {
    // 文件信息
    File FileInfo `json:"file"`
    // 所属通道
    Channel int32 `json:"channel"`
    // 设备成本
    Cost float64 `json:"cost"`
    // 这批设备的Tag
    Tags []string `json:"tags,optional"`    
    // 是否覆盖
    Cover bool `json:"cover"`
    // 设备类型
    DeviceType string `json:"deviceType"`
    // 设备型号
    Model string `json:"model"`
    // 设备序列号前缀
    SerialPrefix string `json:"serialPrefix,optional"`
}

type ImportDeviceResp {
    BaseMsgResp
}

type ManufacturerInfo {
    // ID
    ID int64 `json:"id"`
    // 厂家名称
    Name string `json:"name"`
    // 联系人姓名
    ContactPerson string `json:"contactPerson"`
    // 联系人电话
    ContactPhone string `json:"contactPhone"`
    // 联系人邮箱
    ContactEmail string `json:"contactEmail"`
    // 地址
    Address string `json:"address"`
    // 银行账户
    BankAccount string `json:"bankAccount"`
    // 开户行名称
    BankName string `json:"bankName"`
    // 创建时间
    CreatedAt int64 `json:"createdAt"`
    // 更新时间
    UpdatedAt int64 `json:"updatedAt"`
}

type ManufacturerCreateReq {
    // 厂家ID（修改时必填）
    ID *int64 `json:"id,optional"`
    // 厂家名称
    Name string `json:"name"`
    // 联系人姓名
    ContactPerson string `json:"contactPerson,optional"`
    // 联系人电话
    ContactPhone string `json:"contactPhone,optional"`
    // 联系人邮箱
    ContactEmail string `json:"contactEmail,optional"`
    // 地址
    Address string `json:"address,optional"`
    // 银行账户
    BankAccount string `json:"bankAccount,optional"`
    // 开户行名称
    BankName string `json:"bankName,optional"`
}

type ManufacturerCreateResp {
    BaseMsgResp
}

type ManufacturerListReq {
    Page PageInfo `json:"page"`
    // 厂家名称
    Name *string `json:"name,optional"`
    // 联系人
    ContactPerson *string `json:"contactPerson,optional"`
    // 联系电话
    ContactPhone *string `json:"contactPhone,optional"`
}

type ManufacturerListInfo {
    BaseListInfo
    Data []ManufacturerInfo `json:"data"`
}

type ManufacturerListResp {
    BaseMsgResp
    Data ManufacturerListInfo `json:"data"`
}

@server(
    group: device
    prefix: /device
    middleware: Authority
    jwt: Auth
)

service Esim {
    // 设备导入
    @handler importDevice
    post /import(ImportDeviceReq) returns (ImportDeviceResp)

    // 创建/修改设备厂商
    @handler createManufacturer
    post /manufacturer/create(ManufacturerCreateReq) returns (ManufacturerCreateResp)

    // 获取设备厂商列表
    @handler getManufacturerList
    post /manufacturer/list(ManufacturerListReq) returns (ManufacturerListResp)
}
