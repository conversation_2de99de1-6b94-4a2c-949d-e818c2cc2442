syntax = "v1"
import "base.api"

type Warehouse {
    Id int64 `json:"id"`                           // 云仓ID，主键
    WarehouseCode string `json:"warehouse_code"`   // 云仓编码，唯一标识
    WarehouseName string `json:"warehouse_name"`   // 云仓名称
    CompanyName string `json:"company_name"`       // 云仓公司名称
    ContactPerson string `json:"contact_person"`   // 联系人姓名
    ContactPhone string `json:"contact_phone"`     // 联系电话
    ContactEmail string `json:"contact_email"`     // 联系邮箱
    Address string `json:"address"`                // 云仓详细地址
    Province string `json:"province"`              // 省份
    City string `json:"city"`                      // 城市
    District string `json:"district"`              // 区县
    ApiEndpoint string `json:"api_endpoint"`       // 奇门API接口地址
    AppKey string `json:"app_key"`                 // 奇门API应用密钥
    CustomerID string `json:"customer_id"`         // 奇门API客户ID
    Status int32 `json:"status"`                   // 云仓状态：1-正常 2-停用
    CreatedAt int64 `json:"created_at"`           // 创建时间
    UpdatedAt int64 `json:"updated_at"`           // 更新时间
}

type GetWarehouseListReq {
    Page int64 `form:"page,default=1"`             // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`   // 每页数量，默认20条
    Keyword string `form:"keyword,optional"`       // 搜索关键词，可选，支持云仓名称、编码搜索
    Status int32 `form:"status,optional"`          // 状态筛选，可选：1-正常 2-停用
}

type WarehouseListData {
    List []Warehouse `json:"list"`                 // 云仓列表
    PageInfo PageInfo `json:"page"`           // 分页信息
}

type GetWarehouseListResp {
    BaseMsgResp
    Data WarehouseListData `json:"data"`           // 云仓列表数据
}

type GetWarehouseDetailReq {
    Id int64 `path:"id"`                           // 云仓ID，路径参数
}

type GetWarehouseDetailResp {
    BaseMsgResp
    Data Warehouse `json:"data"`                   // 云仓详细信息
}

type CreateWarehouseReq {
    WarehouseCode string `json:"warehouse_code"`   // 云仓编码，必填，全局唯一
    WarehouseName string `json:"warehouse_name"`   // 云仓名称，必填
    CompanyName string `json:"company_name"`       // 云仓公司名称，必填
    ContactPerson string `json:"contact_person"`   // 联系人姓名，必填
    ContactPhone string `json:"contact_phone"`     // 联系电话，必填
    ContactEmail string `json:"contact_email,optional"` // 联系邮箱，可选
    Address string `json:"address"`                // 云仓详细地址，必填
    Province string `json:"province"`              // 省份，必填
    City string `json:"city"`                      // 城市，必填
    District string `json:"district,optional"`     // 区县，可选
    ApiEndpoint string `json:"api_endpoint"`       // 奇门API接口地址，必填
    AppKey string `json:"app_key"`                 // 奇门API应用密钥，必填
    AppSecret string `json:"app_secret"`           // 奇门API应用密钥，必填
    CustomerID string `json:"customer_id"`         // 奇门API客户ID，必填
}

type CreateWarehouseResp {
    BaseMsgResp
    Data CreateWarehouseData `json:"data"`         // 创建成功返回的数据
}
type CreateWarehouseData {
    Id int64 `json:"id"`                           // 新创建的云仓ID
}

type UpdateWarehouseReq {
    Id int64 `json:"id"`                           // 云仓ID，必填
    WarehouseName string `json:"warehouse_name,optional"`     // 云仓名称，可选
    CompanyName string `json:"company_name,optional"`         // 云仓公司名称，可选
    ContactPerson string `json:"contact_person,optional"`     // 联系人姓名，可选
    ContactPhone string `json:"contact_phone,optional"`       // 联系电话，可选
    ContactEmail string `json:"contact_email,optional"`       // 联系邮箱，可选
    Address string `json:"address,optional"`                  // 云仓详细地址，可选
    Province string `json:"province,optional"`                // 省份，可选
    City string `json:"city,optional"`                        // 城市，可选
    District string `json:"district,optional"`                // 区县，可选
    ApiEndpoint string `json:"api_endpoint,optional"`         // 奇门API接口地址，可选
    AppKey string `json:"app_key,optional"`                   // 奇门API应用密钥，可选
    AppSecret string `json:"app_secret,optional"`             // 奇门API应用密钥，可选
    CustomerID string `json:"customer_id,optional"`           // 奇门API客户ID，可选
    Status int32 `json:"status,optional"`                     // 云仓状态，可选：1-正常 2-停用
}

type UpdateWarehouseResp {
    BaseMsgResp                                       // 更新操作响应，无额外数据
}

// =================== 入库管理 ===================
type EntryOrder {
    Id int64 `json:"id"`                                       // 入库单ID，主键
    EntryOrderCode string `json:"entry_order_code"`           // 入库单号，唯一标识
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码
    WarehouseName string `json:"warehouse_name"`              // 云仓名称
    OwnerCode string `json:"owner_code"`                      // 货主编码，默认NIUYI
    OrderType string `json:"order_type"`                      // 业务类型：SCRK-生产入库 CGRK-采购入库 DBRK-调拨入库
    PurchaseOrderCode string `json:"purchase_order_code"`     // 采购单号，当orderType=CGRK时使用
    ExpectStartTime string `json:"expect_start_time"`         // 预期到货开始时间
    ExpectEndTime string `json:"expect_end_time"`             // 预期到货结束时间
    ActualArrivalTime string `json:"actual_arrival_time"`     // 实际到货时间
    LogisticsCode string `json:"logistics_code"`              // 物流公司编码：SF-顺丰 YTO-圆通等
    LogisticsName string `json:"logistics_name"`              // 物流公司名称
    ExpressCode string `json:"express_code"`                  // 运单号
    SupplierCode string `json:"supplier_code"`                // 供应商编码（设备厂商编码）
    SupplierName string `json:"supplier_name"`                // 供应商名称（设备厂商名称）
    OperatorCode string `json:"operator_code"`                // 操作员编码
    OperatorName string `json:"operator_name"`                // 操作员姓名
    OperateTime string `json:"operate_time"`                  // 操作时间
    TotalOrderLines int32 `json:"total_order_lines"`          // 入库单行数统计
    ExpectedQuantity int32 `json:"expected_quantity"`         // 预期入库数量
    ActualQuantity int32 `json:"actual_quantity"`             // 实际入库数量
    OrderStatus int32 `json:"order_status"`                   // 订单状态：1-待入库 2-部分入库 3-全部入库 4-异常 5-取消
    Remark string `json:"remark"`                             // 备注信息
    QimenEntryOrderID string `json:"qimen_entry_order_id"`    // 奇门系统返回的入库单ID
    ApiSyncStatus int32 `json:"api_sync_status"`              // API同步状态：0-未同步 1-已同步 2-同步失败
    ApiSyncTime string `json:"api_sync_time"`                 // API同步时间
    ApiErrorMessage string `json:"api_error_message"`         // API同步错误信息
    CreatedAt int64 `json:"created_at"`                      // 创建时间
    UpdatedAt int64 `json:"updated_at"`                      // 更新时间
    OrderLines []EntryOrderLine `json:"order_lines,omitempty"` // 入库单明细列表，可选
}

type EntryOrderLine {
    Id int64 `json:"id"`                               // 入库单明细ID，主键
    EntryOrderID int64 `json:"entry_order_id"`        // 入库单ID，外键
    EntryOrderCode string `json:"entry_order_code"`   // 入库单号
    OrderLineNo string `json:"order_line_no"`         // 入库单行号
    OutBizCode string `json:"out_biz_code"`           // 外部业务编码，用于去重
    OwnerCode string `json:"owner_code"`              // 货主编码
    ItemCode string `json:"item_code"`                // 商品编码（设备序列号）
    ItemID string `json:"item_id"`                    // 仓储系统商品ID
    ItemName string `json:"item_name"`                // 商品名称（设备名称）
    SkuProperty string `json:"sku_property"`          // 商品属性
    PlanQty int32 `json:"plan_qty"`                   // 计划入库数量
    ActualQty int32 `json:"actual_qty"`               // 实际入库数量
    PurchasePrice float64 `json:"purchase_price"`     // 采购价格
    RetailPrice float64 `json:"retail_price"`         // 零售价格
    InventoryType string `json:"inventory_type"`      // 库存类型：ZP-正品 CC-残次 JS-机损 XS-箱损
    BatchCode string `json:"batch_code"`              // 批次编码
    ProduceCode string `json:"produce_code"`          // 生产批号
    ProductDate string `json:"product_date"`          // 商品生产日期 YYYY-MM-DD
    ExpireDate string `json:"expire_date"`            // 商品过期日期 YYYY-MM-DD
    BoxNumber string `json:"box_number"`              // 箱号
    PalletNumber string `json:"pallet_number"`        // 卡板号
    Unit string `json:"unit"`                         // 单位：台/个/盒/箱等
    SnCodes []string `json:"sn_codes"`                // SN编码列表
    ShelfLocation string `json:"shelf_location"`      // 上架位置
    InboundTime string `json:"inbound_time"`          // 实际入库时间
    DeviceStatus int32 `json:"device_status"`         // 设备状态：1-正常 2-损坏 3-缺失配件
    Remark string `json:"remark"`                     // 备注
    CreatedAt int64 `json:"created_at"`              // 创建时间
}

type CreateEntryOrderReq {
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码，必填
    OrderType string `json:"order_type,default=CGRK"`        // 业务类型，默认采购入库
    PurchaseOrderCode string `json:"purchase_order_code,optional"` // 采购单号，可选
    ExpectStartTime string `json:"expect_start_time,optional"`     // 预期到货开始时间，可选
    ExpectEndTime string `json:"expect_end_time,optional"`         // 预期到货结束时间，可选
    LogisticsCode string `json:"logistics_code,optional"`          // 物流公司编码，可选
    LogisticsName string `json:"logistics_name,optional"`          // 物流公司名称，可选
    ExpressCode string `json:"express_code,optional"`              // 运单号，可选
    SupplierCode string `json:"supplier_code"`                     // 供应商编码，必填
    SupplierName string `json:"supplier_name"`                     // 供应商名称，必填
    OperatorName string `json:"operator_name,optional"`            // 操作员姓名，可选
    Remark string `json:"remark,optional"`                         // 备注，可选
    OrderLines []CreateEntryOrderLineReq `json:"order_lines"`      // 入库单明细列表，必填
}

type CreateEntryOrderLineReq {
    ItemCode string `json:"item_code"`                        // 商品编码（设备序列号），必填
    ItemName string `json:"item_name"`                        // 商品名称，必填
    PlanQty int32 `json:"plan_qty,default=1"`                 // 计划入库数量，默认1
    PurchasePrice float64 `json:"purchase_price,optional"`    // 采购价格，可选
    RetailPrice float64 `json:"retail_price,optional"`        // 零售价格，可选
    BatchCode string `json:"batch_code,optional"`             // 批次编码，可选
    ProduceCode string `json:"produce_code,optional"`         // 生产批号，可选
    ProductDate string `json:"product_date,optional"`         // 生产日期，可选
    ExpireDate string `json:"expire_date,optional"`           // 过期日期，可选
    BoxNumber string `json:"box_number,optional"`             // 箱号，可选
    PalletNumber string `json:"pallet_number,optional"`       // 卡板号，可选
    SnCodes []string `json:"sn_codes,optional"`               // SN编码列表，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
}

type CreateEntryOrderResp {
    BaseMsgResp
    Data CreateEntryOrderData `json:"data"`                   // 创建成功返回的数据
}

type CreateEntryOrderData {
    Id int64 `json:"id"`                                      // 新创建的入库单ID
    EntryOrderCode string `json:"entry_order_code"`          // 生成的入库单号
}

type GetEntryOrderListReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouse_code,optional"`     // 云仓编码筛选，可选
    OrderStatus int32 `form:"order_status,optional"`          // 订单状态筛选，可选
    SupplierCode string `form:"supplier_code,optional"`       // 供应商编码筛选，可选
    StartTime string `form:"start_time,optional"`             // 开始时间筛选，可选
    EndTime string `form:"end_time,optional"`                 // 结束时间筛选，可选
    Keyword string `form:"keyword,optional"`                  // 关键词搜索，可选，支持入库单号、供应商名称
}

type GetEntryOrderListResp {
    BaseMsgResp
    Data EntryOrderListData `json:"data"`                     // 入库单列表数据
}

type EntryOrderListData {
    List []EntryOrder `json:"list"`                           // 入库单列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type GetEntryOrderDetailReq {
    Id int64 `path:"id"`                                      // 入库单ID，路径参数
}

type GetEntryOrderDetailResp {
    BaseMsgResp
    Data EntryOrder `json:"data"`                             // 入库单详细信息，包含明细
}

type ConfirmEntryOrderReq {
    Id int64 `json:"id"`                                      // 入库单ID，必填
    ActualArrivalTime string `json:"actual_arrival_time,optional"` // 实际到货时间，可选
    OperatorName string `json:"operator_name,optional"`            // 操作员姓名，可选
    Remark string `json:"remark,optional"`                         // 确认备注，可选
    OrderLines []ConfirmEntryOrderLineReq `json:"order_lines,optional"` // 入库明细确认，可选
}

type ConfirmEntryOrderLineReq {
    Id int64 `json:"id"`                                      // 入库单明细ID，必填
    ActualQty int32 `json:"actual_qty"`                       // 实际入库数量，必填
    ShelfLocation string `json:"shelf_location,optional"`     // 上架位置，可选
    DeviceStatus int32 `json:"device_status,optional"`        // 设备状态，可选
    Remark string `json:"remark,optional"`                    // 明细备注，可选
}

type ConfirmEntryOrderResp {
    BaseMsgResp                                                   // 确认操作响应
}

type CancelEntryOrderReq {
    Id int64 `json:"id"`                                      // 入库单ID，必填
    CancelReason string `json:"cancel_reason"`                // 取消原因，必填
    OperatorName string `json:"operator_name,optional"`       // 操作员姓名，可选
}

type CancelEntryOrderResp {
    BaseMsgResp                                                   // 取消操作响应
}

type BatchCreateEntryLinesReq {
    EntryOrderId int64 `json:"entry_order_id"`               // 入库单ID，必填
    OrderLines []CreateEntryOrderLineReq `json:"order_lines"` // 批量入库明细，必填
}

type BatchCreateEntryLinesResp {
    BaseMsgResp
    Data BatchCreateEntryLinesData `json:"data"`              // 批量创建结果
}

type BatchCreateEntryLinesData {
    SuccessCount int32 `json:"success_count"`                 // 成功创建数量
    FailCount int32 `json:"fail_count"`                       // 失败数量
    FailDetails []string `json:"fail_details,omitempty"`      // 失败详情列表
}

// =================== 出库管理 ===================
type DeliveryOrder {
    Id int64 `json:"id"`                                       // 出库单ID，主键
    DeliveryOrderCode string `json:"delivery_order_code"`     // 出库单号，唯一标识
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码
    WarehouseName string `json:"warehouse_name"`              // 云仓名称
    OwnerCode string `json:"owner_code"`                      // 货主编码，默认NIUYI
    OrderType string `json:"order_type"`                      // 业务类型：JYCK-交易出库 DBCK-调拨出库
    OrderCreateTime string `json:"order_create_time"`         // 订单创建时间
    Priority int32 `json:"priority"`                          // 优先级：1-普通 2-紧急 3-特急
    ReceiverName string `json:"receiver_name"`                // 收货人姓名
    ReceiverPhone string `json:"receiver_phone"`              // 收货人电话
    ReceiverMobile string `json:"receiver_mobile"`            // 收货人手机
    ReceiverEmail string `json:"receiver_email"`              // 收货人邮箱
    ReceiverIdType string `json:"receiver_id_type"`           // 收件人证件类型：1-身份证 2-军官证 3-护照 4-其他
    ReceiverIdNumber string `json:"receiver_id_number"`       // 收件人证件号码
    ReceiverProvince string `json:"receiver_province"`        // 收货省份
    ReceiverCity string `json:"receiver_city"`                // 收货城市
    ReceiverArea string `json:"receiver_area"`                // 收货区域
    ReceiverTown string `json:"receiver_town"`                // 收货村镇
    ReceiverAddress string `json:"receiver_address"`          // 收货详细地址
    ReceiverZipCode string `json:"receiver_zip_code"`         // 收货邮编
    RecipientType int32 `json:"recipient_type"`               // 收货人类型：1-合伙人 2-代理商 3-用户
    RecipientId int64 `json:"recipient_id"`                   // 收货人ID
    TradeOrderCode string `json:"trade_order_code"`           // 交易订单号
    ExpectStartTime string `json:"expect_start_time"`         // 预期发货开始时间
    ExpectEndTime string `json:"expect_end_time"`             // 预期发货结束时间
    LogisticsCode string `json:"logistics_code"`              // 物流公司编码
    LogisticsName string `json:"logistics_name"`              // 物流公司名称
    ExpressCode string `json:"express_code"`                  // 运单号
    ExpressFee float64 `json:"express_fee"`                   // 快递费用
    TotalOrderLines int32 `json:"total_order_lines"`          // 出库单行数统计
    RequestedQuantity int32 `json:"requested_quantity"`       // 申请出库数量
    ActualQuantity int32 `json:"actual_quantity"`             // 实际出库数量
    OrderStatus int32 `json:"order_status"`                   // 订单状态：1-待出库 2-已出库 3-已发货 4-已收货 5-异常 6-取消
    ShipTime string `json:"ship_time"`                        // 发货时间
    ReceiveTime string `json:"receive_time"`                  // 收货时间
    OperatorCode string `json:"operator_code"`                // 操作员编码
    OperatorName string `json:"operator_name"`                // 操作员姓名
    OperateTime string `json:"operate_time"`                  // 操作时间
    Remark string `json:"remark"`                             // 备注
    QimenDeliveryOrderID string `json:"qimen_delivery_order_id"` // 奇门系统返回的出库单ID
    ApiSyncStatus int32 `json:"api_sync_status"`              // API同步状态：0-未同步 1-已同步 2-同步失败
    ApiSyncTime string `json:"api_sync_time"`                 // API同步时间
    ApiErrorMessage string `json:"api_error_message"`         // API同步错误信息
    CreatedAt int64 `json:"created_at"`                      // 创建时间
    UpdatedAt int64 `json:"updated_at"`                      // 更新时间
    OrderLines []DeliveryOrderLine `json:"order_lines,omitempty"` // 出库单明细列表，可选
}

type DeliveryOrderLine {
    Id int64 `json:"id"`                                       // 出库单明细ID，主键
    DeliveryOrderID int64 `json:"delivery_order_id"`          // 出库单ID，外键
    DeliveryOrderCode string `json:"delivery_order_code"`     // 出库单号
    OrderLineNo string `json:"order_line_no"`                 // 出库单行号
    OutBizCode string `json:"out_biz_code"`                   // 外部业务编码
    OwnerCode string `json:"owner_code"`                      // 货主编码
    ItemCode string `json:"item_code"`                        // 商品编码（设备序列号）
    ItemID string `json:"item_id"`                            // 仓储系统商品ID
    ItemName string `json:"item_name"`                        // 商品名称
    SkuProperty string `json:"sku_property"`                  // 商品属性
    PlanQty int32 `json:"plan_qty"`                           // 计划出库数量
    ActualQty int32 `json:"actual_qty"`                       // 实际出库数量
    RetailPrice float64 `json:"retail_price"`                 // 零售价格
    InventoryType string `json:"inventory_type"`              // 库存类型
    BatchCode string `json:"batch_code"`                      // 批次编码
    ProduceCode string `json:"produce_code"`                  // 生产批号
    OriginalShelfLocation string `json:"original_shelf_location"` // 原货架位置
    OriginalBoxNumber string `json:"original_box_number"`     // 原箱号
    OriginalPalletNumber string `json:"original_pallet_number"` // 原卡板号
    Unit string `json:"unit"`                                 // 单位
    SnCodes []string `json:"sn_codes"`                        // SN编码列表
    OutboundTime string `json:"outbound_time"`                // 实际出库时间
    Remark string `json:"remark"`                             // 备注
    CreatedAt int64 `json:"created_at"`                      // 创建时间
}

// 出库相关请求响应结构继续...
type CreateDeliveryOrderReq {
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码，必填
    OrderType string `json:"order_type,default=JYCK"`        // 业务类型，默认交易出库
    Priority int32 `json:"priority,default=1"`                // 优先级，默认普通
    ReceiverName string `json:"receiver_name"`                // 收货人姓名，必填
    ReceiverPhone string `json:"receiver_phone"`              // 收货人电话，必填
    ReceiverMobile string `json:"receiver_mobile,optional"`   // 收货人手机，可选
    ReceiverEmail string `json:"receiver_email,optional"`     // 收货人邮箱，可选
    ReceiverIdType string `json:"receiver_id_type,optional"`  // 收件人证件类型，可选
    ReceiverIdNumber string `json:"receiver_id_number,optional"` // 收件人证件号码，可选
    ReceiverProvince string `json:"receiver_province"`        // 收货省份，必填
    ReceiverCity string `json:"receiver_city"`                // 收货城市，必填
    ReceiverArea string `json:"receiver_area,optional"`       // 收货区域，可选
    ReceiverTown string `json:"receiver_town,optional"`       // 收货村镇，可选
    ReceiverAddress string `json:"receiver_address"`          // 收货详细地址，必填
    ReceiverZipCode string `json:"receiver_zip_code,optional"` // 收货邮编，可选
    RecipientType int32 `json:"recipient_type"`               // 收货人类型，必填：1-合伙人 2-代理商 3-用户
    RecipientId int64 `json:"recipient_id"`                   // 收货人ID，必填
    TradeOrderCode string `json:"trade_order_code,optional"`  // 交易订单号，可选
    ExpectStartTime string `json:"expect_start_time,optional"` // 预期发货开始时间，可选
    ExpectEndTime string `json:"expect_end_time,optional"`     // 预期发货结束时间，可选
    LogisticsCode string `json:"logistics_code,optional"`     // 物流公司编码，可选
    LogisticsName string `json:"logistics_name,optional"`     // 物流公司名称，可选
    OperatorName string `json:"operator_name,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
    OrderLines []CreateDeliveryOrderLineReq `json:"order_lines"` // 出库单明细列表，必填
}

type CreateDeliveryOrderLineReq {
    ItemCode string `json:"item_code"`                        // 商品编码（设备序列号），必填
    ItemName string `json:"item_name"`                        // 商品名称，必填
    PlanQty int32 `json:"plan_qty,default=1"`                 // 计划出库数量，默认1
    BatchCode string `json:"batch_code,optional"`             // 指定批次编码，可选
    Remark string `json:"remark,optional"`                    // 备注，可选
}

type CreateDeliveryOrderResp {
    BaseMsgResp
    Data CreateDeliveryOrderData `json:"data"`                // 创建成功返回的数据
}

type CreateDeliveryOrderData {
    Id int64 `json:"id"`                                      // 新创建的出库单ID
    DeliveryOrderCode string `json:"delivery_order_code"`    // 生成的出库单号
}

// =================== 库存管理 ===================
type Inventory {
    Id int64 `json:"id"`                                      // 库存记录ID，主键
    WarehouseCode string `json:"warehouse_code"`             // 云仓编码
    OwnerCode string `json:"owner_code"`                     // 货主编码
    ItemCode string `json:"item_code"`                       // 商品编码（设备序列号）
    ItemID string `json:"item_id"`                           // 仓储系统商品ID
    ItemName string `json:"item_name"`                       // 商品名称
    AvailableQty int32 `json:"available_qty"`                // 可用库存数量
    LockedQty int32 `json:"locked_qty"`                      // 锁定库存数量
    PickedQty int32 `json:"picked_qty"`                      // 拣货库存数量
    TotalQty int32 `json:"total_qty"`                        // 总库存数量
    InventoryType string `json:"inventory_type"`             // 库存类型：ZP-正品 CC-残次等
    InventoryStatus int32 `json:"inventory_status"`          // 库存状态：1-在库 2-预出库 3-已出库 4-损坏 5-丢失
    BatchCode string `json:"batch_code"`                     // 批次编码
    ProduceCode string `json:"produce_code"`                 // 生产批号
    ProductDate string `json:"product_date"`                 // 生产日期
    ExpireDate string `json:"expire_date"`                   // 过期日期
    ShelfLocation string `json:"shelf_location"`             // 货架位置
    BoxNumber string `json:"box_number"`                     // 箱号
    PalletNumber string `json:"pallet_number"`               // 卡板号
    InWarehouseTime string `json:"in_warehouse_time"`        // 入库时间
    OutWarehouseTime string `json:"out_warehouse_time"`      // 出库时间
    LastInventoryTime string `json:"last_inventory_time"`    // 最后盘点时间
    InBatchNo string `json:"in_batch_no"`                    // 入库批次号
    OutBatchNo string `json:"out_batch_no"`                  // 出库批次号
    Remark string `json:"remark"`                            // 备注
    CreatedAt int64 `json:"created_at"`                     // 创建时间
    UpdatedAt int64 `json:"updated_at"`                     // 更新时间
}

type QueryInventoryReq {
    Page int64 `form:"page,default=1"`                       // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`             // 每页数量，默认20条
    WarehouseCode string `form:"warehouse_code,optional"`    // 云仓编码筛选，可选
    ItemCode string `form:"item_code,optional"`              // 商品编码筛选，可选
    InventoryStatus int32 `form:"inventory_status,optional"` // 库存状态筛选，可选
    InventoryType string `form:"inventory_type,optional"`    // 库存类型筛选，可选
    BatchCode string `form:"batch_code,optional"`            // 批次编码筛选，可选
    ShelfLocation string `form:"shelf_location,optional"`    // 货架位置筛选，可选
    MinQty int32 `form:"min_qty,optional"`                   // 最小库存数量筛选，可选
    MaxQty int32 `form:"max_qty,optional"`                   // 最大库存数量筛选，可选
}

type QueryInventoryResp {
    BaseMsgResp
    Data InventoryListData `json:"data"`                     // 库存查询结果
}

type InventoryListData {
    List []Inventory `json:"list"`                           // 库存列表
    PageInfo PageInfo `json:"page"`                     // 分页信息
    Summary InventorySummary `json:"summary"`                // 库存汇总信息
}

type InventorySummary {
    TotalItems int64 `json:"total_items"`                    // 总商品种类数
    TotalQty int64 `json:"total_qty"`                        // 总库存数量
    AvailableQty int64 `json:"available_qty"`                // 总可用库存
    LockedQty int64 `json:"locked_qty"`                      // 总锁定库存
}

// =================== API同步日志 ===================
type QimenSyncLog {
    Id int64 `json:"id"`                                      // 同步日志ID，主键
    WarehouseCode string `json:"warehouse_code"`             // 云仓编码
    ApiMethod string `json:"api_method"`                     // API方法名
    SyncType int32 `json:"sync_type"`                        // 同步类型：1-入库单 2-出库单 3-库存查询 4-库存同步
    SyncDirection int32 `json:"sync_direction"`              // 同步方向：1-推送到云仓 2-从云仓拉取
    RelatedOrderId int64 `json:"related_order_id"`           // 关联订单ID
    RelatedOrderCode string `json:"related_order_code"`      // 关联订单号
    SyncStatus int32 `json:"sync_status"`                    // 同步状态：1-成功 2-失败 3-部分成功
    ErrorCode string `json:"error_code"`                     // 错误码
    ErrorMessage string `json:"error_message"`               // 错误信息
    RetryCount int32 `json:"retry_count"`                    // 重试次数
    MaxRetryCount int32 `json:"max_retry_count"`             // 最大重试次数
    NextRetryTime string `json:"next_retry_time"`            // 下次重试时间
    SyncTime string `json:"sync_time"`                       // 同步时间
    ResponseTime int32 `json:"response_time"`                // 响应时间（毫秒）
    CreatedAt int64 `json:"created_at"`                     // 创建时间
}

type GetQimenSyncLogsReq {
    Page int64 `form:"page,default=1"`                       // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`             // 每页数量，默认20条
    WarehouseCode string `form:"warehouse_code,optional"`    // 云仓编码筛选，可选
    SyncType int32 `form:"sync_type,optional"`               // 同步类型筛选，可选
    SyncStatus int32 `form:"sync_status,optional"`           // 同步状态筛选，可选
    StartTime string `form:"start_time,optional"`            // 开始时间筛选，可选
    EndTime string `form:"end_time,optional"`                // 结束时间筛选，可选
    RelatedOrderCode string `form:"related_order_code,optional"` // 关联订单号筛选，可选
}

type GetQimenSyncLogsResp {
    BaseMsgResp
    Data QimenSyncLogListData `json:"data"`                  // 同步日志列表数据
}

type QimenSyncLogListData {
    List []QimenSyncLog `json:"list"`                        // 同步日志列表
    PageInfo PageInfo `json:"page"`                     // 分页信息
}

// =================== 同步操作请求响应 ===================
type SyncEntryOrderToQimenReq {
    EntryOrderId int64 `json:"entry_order_id"`               // 入库单ID，必填
    ForceSync bool `json:"force_sync,default=false"`         // 是否强制同步，默认false
}

type SyncEntryOrderToQimenResp {
    BaseMsgResp
    Data SyncResultData `json:"data"`                        // 同步结果数据
}

type SyncResultData {
    SyncLogId int64 `json:"sync_log_id"`                     // 同步日志ID
    QimenOrderId string `json:"qimen_order_id,omitempty"`    // 奇门系统订单ID
    SyncStatus int32 `json:"sync_status"`                    // 同步状态
    Message string `json:"message"`                          // 同步结果消息
}

type SyncDeliveryOrderToQimenReq {
    DeliveryOrderId int64 `json:"delivery_order_id"`         // 出库单ID，必填
    ForceSync bool `json:"force_sync,default=false"`         // 是否强制同步，默认false
}

type SyncDeliveryOrderToQimenResp {
    BaseMsgResp
    Data SyncResultData `json:"data"`                        // 同步结果数据
}

type SyncInventoryFromQimenReq {
    WarehouseCode string `json:"warehouse_code"`             // 云仓编码，必填
    ItemCodes []string `json:"item_codes,optional"`          // 指定商品编码列表，可选，为空则同步所有
}

type SyncInventoryFromQimenResp {
    BaseMsgResp
    Data InventorySyncResultData `json:"data"`               // 库存同步结果
}

type InventorySyncResultData {
    SyncLogId int64 `json:"sync_log_id"`                     // 同步日志ID
    SyncCount int32 `json:"sync_count"`                      // 同步数量
    SuccessCount int32 `json:"success_count"`                // 成功数量
    FailCount int32 `json:"fail_count"`                      // 失败数量
    Message string `json:"message"`                          // 同步结果消息
}

type RetryQimenSyncReq {
    SyncLogIds []int64 `json:"sync_log_ids"`                 // 同步日志ID列表，必填
}

type RetryQimenSyncResp {
    BaseMsgResp
    Data RetryResultData `json:"data"`                       // 重试结果数据
}

type RetryResultData {
    TotalCount int32 `json:"total_count"`                    // 总重试数量
    SuccessCount int32 `json:"success_count"`                // 成功数量
    FailCount int32 `json:"fail_count"`                      // 失败数量
    Details []RetryDetail `json:"details,omitempty"`         // 重试详情
}

type RetryDetail {
    SyncLogId int64 `json:"sync_log_id"`                     // 同步日志ID
    Success bool `json:"success"`                            // 是否成功
    Message string `json:"message"`                          // 结果消息
}

// 出库单列表相关
type GetDeliveryOrderListReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouse_code,optional"`     // 云仓编码筛选，可选
    OrderStatus int32 `form:"order_status,optional"`          // 订单状态筛选，可选
    RecipientType int32 `form:"recipient_type,optional"`      // 收货人类型筛选，可选
    StartTime string `form:"start_time,optional"`             // 开始时间筛选，可选
    EndTime string `form:"end_time,optional"`                 // 结束时间筛选，可选
    Keyword string `form:"keyword,optional"`                  // 关键词搜索，可选，支持出库单号、收货人姓名
}

type GetDeliveryOrderListResp {
    BaseMsgResp
    Data DeliveryOrderListData `json:"data"`                  // 出库单列表数据
}

type DeliveryOrderListData {
    List []DeliveryOrder `json:"list"`                        // 出库单列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type GetDeliveryOrderDetailReq {
    Id int64 `path:"id"`                                      // 出库单ID，路径参数
}

type GetDeliveryOrderDetailResp {
    BaseMsgResp
    Data DeliveryOrder `json:"data"`                          // 出库单详细信息，包含明细
}

// 出库确认相关
type ConfirmDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    OperatorName string `json:"operator_name,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 确认备注，可选
    OrderLines []ConfirmDeliveryOrderLineReq `json:"order_lines,optional"` // 出库明细确认，可选
}

type ConfirmDeliveryOrderLineReq {
    Id int64 `json:"id"`                                      // 出库单明细ID，必填
    ActualQty int32 `json:"actual_qty"`                       // 实际出库数量，必填
    OriginalShelfLocation string `json:"original_shelf_location,optional"` // 原货架位置，可选
    Remark string `json:"remark,optional"`                    // 明细备注，可选
}

type ConfirmDeliveryOrderResp {
    BaseMsgResp                                                // 确认操作响应
}

// 发货确认相关
type ShipDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    LogisticsCode string `json:"logistics_code"`              // 物流公司编码，必填
    LogisticsName string `json:"logistics_name"`              // 物流公司名称，必填
    ExpressCode string `json:"express_code"`                  // 运单号，必填
    ExpressFee float64 `json:"express_fee,optional"`          // 快递费用，可选
    ShipTime string `json:"ship_time,optional"`               // 发货时间，可选，默认当前时间
    OperatorName string `json:"operator_name,optional"`       // 操作员姓名，可选
    Remark string `json:"remark,optional"`                    // 发货备注，可选
}

type ShipDeliveryOrderResp {
    BaseMsgResp                                                // 发货操作响应
}

// 收货确认相关
type ReceiveDeliveryOrderReq {
    Id int64 `json:"id"`                                      // 出库单ID，必填
    ReceiveTime string `json:"receive_time,optional"`         // 收货时间，可选，默认当前时间
    ReceiverName string `json:"receiver_name,optional"`       // 实际收货人姓名，可选
    Remark string `json:"remark,optional"`                    // 收货备注，可选
}

type ReceiveDeliveryOrderResp {
    BaseMsgResp                                                // 收货操作响应
}

// 库存相关缺少的类型
type InventoryCheckReq {
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码，必填
    CheckType int32 `json:"check_type,default=1"`             // 盘点类型：1-全盘 2-抽盘 3-循环盘点
    ItemCodes []string `json:"item_codes,optional"`           // 指定商品编码列表，可选
    ShelfLocations []string `json:"shelf_locations,optional"` // 指定货架位置列表，可选
    OperatorName string `json:"operator_name"`                // 操作员姓名，必填
    Remark string `json:"remark,optional"`                    // 盘点备注，可选
}

type InventoryCheckResp {
    BaseMsgResp
    Data InventoryCheckData `json:"data"`                     // 盘点结果数据
}

type InventoryCheckData {
    CheckId int64 `json:"check_id"`                           // 盘点任务ID
    TotalItems int32 `json:"total_items"`                     // 盘点商品总数
    CheckedItems int32 `json:"checked_items"`                 // 已盘点商品数
    DiffItems int32 `json:"diff_items"`                       // 差异商品数
    Status int32 `json:"status"`                              // 盘点状态：1-进行中 2-已完成
}

type AdjustInventoryReq {
    WarehouseCode string `json:"warehouse_code"`              // 云仓编码，必填
    ItemCode string `json:"item_code"`                        // 商品编码，必填
    AdjustType int32 `json:"adjust_type"`                     // 调整类型：1-增加 2-减少
    AdjustQty int32 `json:"adjust_qty"`                       // 调整数量，必填
    AdjustReason string `json:"adjust_reason"`                // 调整原因，必填
    BatchCode string `json:"batch_code,optional"`             // 批次编码，可选
    OperatorName string `json:"operator_name"`                // 操作员姓名，必填
    Remark string `json:"remark,optional"`                    // 调整备注，可选
}

type AdjustInventoryResp {
    BaseMsgResp
    Data AdjustInventoryData `json:"data"`                    // 调整结果数据
}

type AdjustInventoryData {
    BeforeQty int32 `json:"before_qty"`                       // 调整前数量
    AfterQty int32 `json:"after_qty"`                         // 调整后数量
    AdjustQty int32 `json:"adjust_qty"`                       // 调整数量
}

type GetInventoryLogsReq {
    Page int64 `form:"page,default=1"`                        // 页码，默认第1页
    PageSize int64 `form:"page_size,default=20"`              // 每页数量，默认20条
    WarehouseCode string `form:"warehouse_code,optional"`     // 云仓编码筛选，可选
    ItemCode string `form:"item_code,optional"`               // 商品编码筛选，可选
    OperationType int32 `form:"operation_type,optional"`      // 操作类型筛选，可选
    StartTime string `form:"start_time,optional"`             // 开始时间筛选，可选
    EndTime string `form:"end_time,optional"`                 // 结束时间筛选，可选
}

type GetInventoryLogsResp {
    BaseMsgResp
    Data InventoryLogListData `json:"data"`                   // 库存日志列表数据
}

type InventoryLogListData {
    List []InventoryLog `json:"list"`                         // 库存日志列表
    PageInfo PageInfo `json:"page"`                      // 分页信息
}

type InventoryLog {
    Id int64 `json:"id"`                                      // 日志ID，主键
    WarehouseCode string `json:"warehouse_code"`             // 云仓编码
    OwnerCode string `json:"owner_code"`                     // 货主编码
    ItemCode string `json:"item_code"`                       // 商品编码
    ItemName string `json:"item_name"`                       // 商品名称
    OperationType int32 `json:"operation_type"`              // 操作类型：1-入库 2-出库 3-盘点 4-调拨 5-报损 6-报溢
    OperationDesc string `json:"operation_desc"`             // 操作描述
    OperationQuantity int32 `json:"operation_quantity"`      // 操作数量
    BeforeQty int32 `json:"before_qty"`                      // 操作前数量
    AfterQty int32 `json:"after_qty"`                        // 操作后数量
    RelatedOrderId int64 `json:"related_order_id"`           // 关联订单ID
    RelatedOrderCode string `json:"related_order_code"`      // 关联订单号
    RelatedOrderType int32 `json:"related_order_type"`       // 关联订单类型
    BatchCode string `json:"batch_code"`                     // 批次编码
    InventoryType string `json:"inventory_type"`             // 库存类型
    OperatorId int64 `json:"operator_id"`                    // 操作员ID
    OperatorName string `json:"operator_name"`               // 操作员姓名
    OperationTime string `json:"operation_time"`             // 操作时间
    Remark string `json:"remark"`                            // 备注
    CreatedAt int64 `json:"created_at"`                      // 创建时间
}


// =================== 云仓基础信息管理 ===================
@server(
    group: warehouse
    prefix: /warehouse
)
service Esim {
    @doc "获取云仓列表"
    @handler GetWarehouseList
    get /list (GetWarehouseListReq) returns (GetWarehouseListResp)
    
    @doc "获取云仓详情"
    @handler GetWarehouseDetail
    get /detail/:id (GetWarehouseDetailReq) returns (GetWarehouseDetailResp)
    
    @doc "创建云仓"
    @handler CreateWarehouse
    post /create (CreateWarehouseReq) returns (CreateWarehouseResp)
    
    @doc "更新云仓"
    @handler UpdateWarehouse
    put /update (UpdateWarehouseReq) returns (UpdateWarehouseResp)
}

// =================== 入库管理 ===================
@server(
    group: entry
    prefix: /warehouse/entry
)
service Esim {
    @doc "创建入库单"
    @handler CreateEntryOrder
    post /order/create (CreateEntryOrderReq) returns (CreateEntryOrderResp)
    
    @doc "获取入库单列表"
    @handler GetEntryOrderList
    get /order/list (GetEntryOrderListReq) returns (GetEntryOrderListResp)
    
    @doc "获取入库单详情"
    @handler GetEntryOrderDetail
    get /order/detail/:id (GetEntryOrderDetailReq) returns (GetEntryOrderDetailResp)
    
    @doc "确认入库"
    @handler ConfirmEntryOrder
    put /order/confirm (ConfirmEntryOrderReq) returns (ConfirmEntryOrderResp)
    
    @doc "取消入库单"
    @handler CancelEntryOrder
    put /order/cancel (CancelEntryOrderReq) returns (CancelEntryOrderResp)
    
    @doc "批量录入入库明细"
    @handler BatchCreateEntryLines
    post /lines/batch (BatchCreateEntryLinesReq) returns (BatchCreateEntryLinesResp)
}

// =================== 出库管理 ===================
@server(
    group: delivery
    prefix: /warehouse/delivery
)
service Esim {
    @doc "创建出库单"
    @handler CreateDeliveryOrder
    post /order/create (CreateDeliveryOrderReq) returns (CreateDeliveryOrderResp)
    
    @doc "获取出库单列表"
    @handler GetDeliveryOrderList
    get /order/list (GetDeliveryOrderListReq) returns (GetDeliveryOrderListResp)
    
    @doc "获取出库单详情"
    @handler GetDeliveryOrderDetail
    get /order/detail/:id (GetDeliveryOrderDetailReq) returns (GetDeliveryOrderDetailResp)
    
    @doc "确认出库"
    @handler ConfirmDeliveryOrder
    put /order/confirm (ConfirmDeliveryOrderReq) returns (ConfirmDeliveryOrderResp)
    
    @doc "发货确认"
    @handler ShipDeliveryOrder
    put /order/ship (ShipDeliveryOrderReq) returns (ShipDeliveryOrderResp)
    
    @doc "收货确认"
    @handler ReceiveDeliveryOrder
    put /order/receive (ReceiveDeliveryOrderReq) returns (ReceiveDeliveryOrderResp)
}

// =================== 库存管理 ===================
@server(
    group: inventory
    prefix: /warehouse/inventory
)
service Esim {
    @doc "查询库存"
    @handler QueryInventory
    get /query (QueryInventoryReq) returns (QueryInventoryResp)
    
    @doc "库存盘点"
    @handler InventoryCheck
    post /check (InventoryCheckReq) returns (InventoryCheckResp)
    
    @doc "库存调整"
    @handler AdjustInventory
    post /adjust (AdjustInventoryReq) returns (AdjustInventoryResp)
    
    @doc "获取库存变动日志"
    @handler GetInventoryLogs
    get /logs (GetInventoryLogsReq) returns (GetInventoryLogsResp)
}

// =================== 奇门API同步 ===================
@server(
    group: qimen
    prefix: /warehouse/qimen
)
service Esim {
    @doc "手动同步入库单到奇门"
    @handler SyncEntryOrderToQimen
    post /sync/entry (SyncEntryOrderToQimenReq) returns (SyncEntryOrderToQimenResp)
    
    @doc "手动同步出库单到奇门"
    @handler SyncDeliveryOrderToQimen
    post /sync/delivery (SyncDeliveryOrderToQimenReq) returns (SyncDeliveryOrderToQimenResp)
    
    @doc "从奇门同步库存"
    @handler SyncInventoryFromQimen
    post /sync/inventory (SyncInventoryFromQimenReq) returns (SyncInventoryFromQimenResp)
    
    @doc "获取API同步日志"
    @handler GetQimenSyncLogs
    get /sync/logs (GetQimenSyncLogsReq) returns (GetQimenSyncLogsResp)
    
    @doc "重试失败的同步任务"
    @handler RetryQimenSync
    post /sync/retry (RetryQimenSyncReq) returns (RetryQimenSyncResp)
}